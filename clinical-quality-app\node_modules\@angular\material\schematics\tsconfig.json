{
  "compilerOptions": {
    "lib": ["es2022"],
    "target": "es2022",
    "module": "CommonJS",
    "moduleResolution": "node10",
    "strict": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "declaration": true,
    "types": ["jasmine", "node"],
    "paths": {
      "@angular/cdk/schematics": ["../../cdk/schematics"],
      "@angular/cdk/schematics/*": ["../../cdk/schematics/*"]
    }
  },
  "exclude": [
    "**/*.spec.ts",
    // Exclude the test-setup utility files. Those should not be part of the output.
    "test-setup/**/*.ts",
    // Exclude template files that will be copied by the schematics. Those are not valid TS.
    "ng-generate/*/files/**/*.ts",
    // Exclude all test-case files because those should not be included in the schematics output.
    "ng-update/test-cases/**/*.ts"
  ]
}
