{"inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs": {"bytes": 8692, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs": {"bytes": 3442, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs": {"bytes": 10987, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs": {"bytes": 8480, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs": {"bytes": 13603, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs": {"bytes": 15766, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.mjs": {"bytes": 9657, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs": {"bytes": 2855, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytes": 1414, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs": {"bytes": 49117, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs": {"bytes": 2595, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs": {"bytes": 2949, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs": {"bytes": 11368, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs": {"bytes": 1629, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs": {"bytes": 4700, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs": {"bytes": 2515, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs": {"bytes": 22814, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs": {"bytes": 3891, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs": {"bytes": 56818, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs": {"bytes": 22884, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs": {"bytes": 5595, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs": {"bytes": 10271, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs": {"bytes": 20092, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs": {"bytes": 12939, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs": {"bytes": 34753, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs": {"bytes": 36831, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs": {"bytes": 5035, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs": {"bytes": 98809, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs": {"bytes": 25138, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs": {"bytes": 16791, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs": {"bytes": 14073, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs": {"bytes": 2170, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs": {"bytes": 18294, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs": {"bytes": 4182, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs": {"bytes": 4209, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.mjs": {"bytes": 81591, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs": {"bytes": 6004, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs": {"bytes": 52789, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs": {"bytes": 27260, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs": {"bytes": 18009, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs": {"bytes": 19840, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs": {"bytes": 34788, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs": {"bytes": 44878, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs": {"bytes": 10450, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs": {"bytes": 9266, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs": {"bytes": 12172, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs": {"bytes": 10374, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs": {"bytes": 13165, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs": {"bytes": 3100, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs": {"bytes": 18787, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs": {"bytes": 6556, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs": {"bytes": 23614, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs": {"bytes": 7242, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs": {"bytes": 1711, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs": {"bytes": 110105, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs": {"bytes": 4096, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs": {"bytes": 2081, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs": {"bytes": 16918, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs": {"bytes": 4256, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs": {"bytes": 28116, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs": {"bytes": 2001, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs": {"bytes": 1861, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs": {"bytes": 15411, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs": {"bytes": 1310, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs": {"bytes": 18126, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs": {"bytes": 92478, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs": {"bytes": 2378, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs": {"bytes": 5955, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs": {"bytes": 15454, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs": {"bytes": 7856, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs": {"bytes": 17340, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs": {"bytes": 58641, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs": {"bytes": 61933, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs": {"bytes": 21031, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs": {"bytes": 2765, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs": {"bytes": 41066, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs": {"bytes": 40744, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs": {"bytes": 4238, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs": {"bytes": 3180, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs": {"bytes": 26291, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs": {"bytes": 14002, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs": {"bytes": 60741, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs": {"bytes": 2121, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs": {"bytes": 55639, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs": {"bytes": 12437, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs": {"bytes": 3361, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs": {"bytes": 5316, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs": {"bytes": 26582, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs": {"bytes": 3967, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs": {"bytes": 2031, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs": {"bytes": 6615, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs": {"bytes": 3639, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.mjs": {"bytes": 1625, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs": {"bytes": 2268, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs": {"bytes": 10458, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs": {"bytes": 31906, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs": {"bytes": 8335, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs": {"bytes": 7126, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs": {"bytes": 1898, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs": {"bytes": 4556, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs": {"bytes": 3751, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs": {"bytes": 17602, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs": {"bytes": 10753, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs": {"bytes": 96780, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs": {"bytes": 16890, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs": {"bytes": 2321, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs": {"bytes": 4446, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs": {"bytes": 18950, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs": {"bytes": 4691, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs": {"bytes": 8062, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs": {"bytes": 8115, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs": {"bytes": 9689, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs": {"bytes": 19470, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs": {"bytes": 188766, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs": {"bytes": 18643, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs": {"bytes": 45769, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs": {"bytes": 1901, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs": {"bytes": 17933, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs": {"bytes": 136252, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs": {"bytes": 1456, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs": {"bytes": 6043, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs": {"bytes": 69898, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs": {"bytes": 12412, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs": {"bytes": 21121, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs": {"bytes": 4851, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs": {"bytes": 12814, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs": {"bytes": 3034, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs": {"bytes": 26735, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs": {"bytes": 7946, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs": {"bytes": 1739, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs": {"bytes": 30936, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs": {"bytes": 1124, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs": {"bytes": 20872, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs": {"bytes": 16526, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs": {"bytes": 4945, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs": {"bytes": 4180, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs": {"bytes": 5246, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs": {"bytes": 15890, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs": {"bytes": 1356, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs": {"bytes": 21341, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs": {"bytes": 2889, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs": {"bytes": 1021, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs": {"bytes": 22323, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs": {"bytes": 27671, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}]}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytes": 38222, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs": {"bytes": 16333, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs": {"bytes": 11220, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs": {"bytes": 20001, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs": {"bytes": 15195, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs": {"bytes": 25126, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs": {"bytes": 34057, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs": {"bytes": 17674, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs": {"bytes": 61177, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs": {"bytes": 5369, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs": {"bytes": 13461, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs": {"bytes": 75860, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs": {"bytes": 392155, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs": {"bytes": 14805, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs": {"bytes": 66492, "imports": [{"path": "node_modules/magic-string/dist/magic-string.es.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs": {"bytes": 7300, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs": {"bytes": 9457, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs": {"bytes": 102024, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs": {"bytes": 126664, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs": {"bytes": 1648, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.mjs": {"bytes": 48021, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.mjs": {"bytes": 6947, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.mjs": {"bytes": 8173, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.mjs": {"bytes": 1027, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs": {"bytes": 275095, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs": {"bytes": 1012, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs": {"bytes": 48128, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs": {"bytes": 30270, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs": {"bytes": 3391, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs": {"bytes": 6005, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.mjs": {"bytes": 9824, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.mjs": {"bytes": 10167, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.mjs": {"bytes": 6558, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.mjs": {"bytes": 10242, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.mjs": {"bytes": 14737, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.mjs": {"bytes": 7707, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.mjs": {"bytes": 1212, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytes": 15078, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytes": 2422, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytes": 7576, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytes": 10492, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytes": 6635, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs": {"bytes": 15654, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs": {"bytes": 17744, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs": {"bytes": 1181, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs": {"bytes": 11780, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs": {"bytes": 1533, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs": {"bytes": 2365, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs": {"bytes": 10863, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs": {"bytes": 1806, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs": {"bytes": 20788, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.mjs": {"bytes": 3715, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs": {"bytes": 66100, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs": {"bytes": 13971, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs": {"bytes": 18653, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs": {"bytes": 6915, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs": {"bytes": 22881, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs": {"bytes": 3393, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.mjs": {"bytes": 4896, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs": {"bytes": 25800, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs": {"bytes": 1018, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs": {"bytes": 4592, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs": {"bytes": 4913, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs": {"bytes": 18811, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs": {"bytes": 8829, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs": {"bytes": 1581, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs": {"bytes": 14212, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs": {"bytes": 6154, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs": {"bytes": 46876, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs": {"bytes": 1497, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs": {"bytes": 9426, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs": {"bytes": 1615, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs": {"bytes": 8215, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs": {"bytes": 3513, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs": {"bytes": 55863, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs": {"bytes": 6595, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs": {"bytes": 1237, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs": {"bytes": 15180, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs": {"bytes": 34542, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs": {"bytes": 979, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs": {"bytes": 17028, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs": {"bytes": 5583, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs": {"bytes": 24880, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs": {"bytes": 1436, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs": {"bytes": 1045, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs": {"bytes": 17082, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs": {"bytes": 5885, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs": {"bytes": 12380, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs": {"bytes": 5896, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs": {"bytes": 10302, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs": {"bytes": 11063, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs": {"bytes": 5693, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs": {"bytes": 8672, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.mjs": {"bytes": 13198, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.mjs": {"bytes": 9106, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.mjs": {"bytes": 7136, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs": {"bytes": 6312, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs": {"bytes": 6996, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs": {"bytes": 7088, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs": {"bytes": 25067, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs": {"bytes": 1235, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs": {"bytes": 12296, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs": {"bytes": 6878, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs": {"bytes": 18479, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.mjs": {"bytes": 12215, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.mjs": {"bytes": 20605, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.mjs": {"bytes": 1348, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.mjs": {"bytes": 8171, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.mjs": {"bytes": 1016, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs": {"bytes": 3689, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs": {"bytes": 3303, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs": {"bytes": 212826, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs": {"bytes": 40811, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs": {"bytes": 1058, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytes": 46938, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytes": 1826, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytes": 1146, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytes": 3339, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytes": 38248, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytes": 3479, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs": {"bytes": 1762, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs": {"bytes": 4417, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs": {"bytes": 1136, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytes": 17569, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytes": 5717, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytes": 38786, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytes": 23553, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytes": 3022, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytes": 6535, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytes": 5030, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/main.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytes": 2374, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytes": 2550, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytes": 1982, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytes": 32574, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytes": 4201, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytes": 10122, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytes": 4603, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytes": 3448, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs": {"bytes": 6109, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytes": 4378, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytes": 13317, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytes": 31815, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytes": 47450, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytes": 6797, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytes": 8182, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytes": 5331, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytes": 14732, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytes": 6045, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytes": 27686, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytes": 13450, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs": {"bytes": 3445, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs": {"bytes": 5110, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs": {"bytes": 60849, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs": {"bytes": 32803, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs": {"bytes": 1444, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytes": 2996, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytes": 4145, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytes": 5499, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytes": 3470, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytes": 2018, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytes": 24004, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytes": 22941, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytes": 7922, "imports": []}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytes": 21003, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytes": 4393, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytes": 1318, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytes": 1230, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytes": 1534, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs", "kind": "import-statement"}]}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytes": 2786, "imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs", "kind": "import-statement"}]}}, "outputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/migrations.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["DynamicValue", "ImportManager", "PartialEvaluator", "PotentialImportKind", "PotentialImportMode", "Reference", "StaticInterpreter", "TypeScriptReflectionHost", "createForwardRefResolver", "reflectObjectLiteral"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/migrations.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/migrations.mjs": {"bytesInOutput": 0}}, "bytes": 981}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/tooling.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGNEM2AI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "constructorParametersDownlevelTransform"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/tooling.mjs", "inputs": {}, "bytes": 611}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 1866}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/index.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGNEM2AI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-FKXFEX7K.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "ErrorCode", "GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "MemberTags", "MemberType", "NgTscPlugin", "NgtscCompilerHost", "NgtscProgram", "NodeJSFileSystem", "OptimizeFor", "SOURCE", "UNKNOWN_ERROR_CODE", "VERSION", "absoluteFrom", "absoluteFromSourceFile", "angularJitApplicationTransform", "basename", "calcProjectFileAndBasePath", "constructorParametersDownlevelTransform", "createCompilerHost", "createFileSystemTsReadDirectoryFn", "createProgram", "defaultGatherDiagnostics", "dirname", "exitCodeFromResult", "formatDiagnostics", "getDownlevelDecoratorsTransform", "getFileSystem", "getInitializerApiJitTransform", "getSourceFileOrError", "isDocEntryWithSourceInfo", "isLocalCompilationDiagnostics", "isLocalRelativePath", "isRoot", "isRooted", "isTsDiagnostic", "join", "ngErrorCode", "performCompilation", "readConfiguration", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/index.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version.mjs": {"bytesInOutput": 93}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/index.mjs": {"bytesInOutput": 39}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/tsc_plugin.mjs": {"bytesInOutput": 2645}}, "bytes": 6024}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGNEM2AI.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 292}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGNEM2AI.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}], "exports": ["GLOBAL_DEFS_FOR_TERSER", "GLOBAL_DEFS_FOR_TERSER_WITH_AOT", "constructorParametersDownlevelTransform"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/tooling.mjs": {"bytesInOutput": 310}}, "bytes": 948}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 392}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ngc.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-NNCRCOQI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs": {"bytesInOutput": 337}}, "bytes": 1125}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 693}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/ngcc/index.js": {"imports": [], "exports": [], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/ngcc/index.mjs": {"bytesInOutput": 1397}}, "bytes": 1853}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 891}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/src/bin/ng_xi18n.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-NNCRCOQI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": [], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/bin/ng_xi18n.mjs": {"bytesInOutput": 197}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/extract_i18n.mjs": {"bytesInOutput": 859}}, "bytes": 2058}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-NNCRCOQI.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 6917}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-NNCRCOQI.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js", "kind": "import-statement"}], "exports": ["main", "readCommandLineAndConfiguration"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/main.mjs": {"bytesInOutput": 4144}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_watch.mjs": {"bytesInOutput": 8117}}, "bytes": 13118}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 112202}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-GGYBKDGO.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}], "exports": ["DEFAULT_ERROR_CODE", "DecoratorType", "DocsExtractor", "EmitFlags", "EntryType", "MemberTags", "MemberType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgCompilerHost", "NgtscProgram", "PatchedProgramIncrementalBuildStrategy", "SOURCE", "UNKNOWN_ERROR_CODE", "calcProjectFileAndBasePath", "createCompilerHost", "createMessageDiagnostic", "createProgram", "defaultGatherDiagnostics", "exitCodeFromResult", "formatDiagnostics", "freshCompilationTicket", "incrementalFromStateTicket", "isDocEntryWithSourceInfo", "isTsDiagnostic", "performCompilation", "readConfiguration"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/api.mjs": {"bytesInOutput": 618}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/compiler_host.mjs": {"bytesInOutput": 242}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/entities.mjs": {"bytesInOutput": 1593}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/extractor.mjs": {"bytesInOutput": 3839}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/class_extractor.mjs": {"bytesInOutput": 12800}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/filters.mjs": {"bytesInOutput": 158}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/function_extractor.mjs": {"bytesInOutput": 4616}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/generics_extractor.mjs": {"bytesInOutput": 404}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/jsdoc_extractor.mjs": {"bytesInOutput": 1833}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_extractor.mjs": {"bytesInOutput": 190}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/internal.mjs": {"bytesInOutput": 575}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/constant_extractor.mjs": {"bytesInOutput": 2409}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/decorator_extractor.mjs": {"bytesInOutput": 3205}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/enum_extractor.mjs": {"bytesInOutput": 1125}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/initializer_api_function_extractor.mjs": {"bytesInOutput": 4140}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/type_alias_extractor.mjs": {"bytesInOutput": 361}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/src/import_extractor.mjs": {"bytesInOutput": 965}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/docs/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program.mjs": {"bytesInOutput": 9126}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/i18n.mjs": {"bytesInOutput": 1466}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/typescript_support.mjs": {"bytesInOutput": 520}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/version_helpers.mjs": {"bytesInOutput": 929}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/compiler.mjs": {"bytesInOutput": 41944}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/analyzer.mjs": {"bytesInOutput": 1795}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/cycles/src/imports.mjs": {"bytesInOutput": 2655}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/generator.mjs": {"bytesInOutput": 832}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/logic.mjs": {"bytesInOutput": 495}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/private_export_checker.mjs": {"bytesInOutput": 2939}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/entry_point/src/reference_graph.mjs": {"bytesInOutput": 1381}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/dependency_tracking.mjs": {"bytesInOutput": 1945}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/state.mjs": {"bytesInOutput": 337}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/incremental.mjs": {"bytesInOutput": 7400}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/noop.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/src/strategy.mjs": {"bytesInOutput": 788}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/api.mjs": {"bytesInOutput": 752}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/context.mjs": {"bytesInOutput": 133}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/transform.mjs": {"bytesInOutput": 1129}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/indexer/src/template.mjs": {"bytesInOutput": 9973}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/ng_module_index.mjs": {"bytesInOutput": 3136}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/src/loader.mjs": {"bytesInOutput": 5812}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/resource/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/standalone.mjs": {"bytesInOutput": 3648}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/interpolated_signal_not_invoked/index.mjs": {"bytesInOutput": 2928}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/symbol_util.mjs": {"bytesInOutput": 926}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/api.mjs": {"bytesInOutput": 3858}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/api/extended_template_checker.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/invalid_banana_in_box/index.mjs": {"bytesInOutput": 964}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_control_flow_directive/index.mjs": {"bytesInOutput": 2264}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/missing_ngforof_let/index.mjs": {"bytesInOutput": 967}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/nullish_coalescing_not_nullable/index.mjs": {"bytesInOutput": 1719}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/optional_chain_not_nullable/index.mjs": {"bytesInOutput": 1911}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/suffix_not_supported/index.mjs": {"bytesInOutput": 869}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/text_attribute_not_binding/index.mjs": {"bytesInOutput": 1401}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/uninvoked_function_in_event_binding/index.mjs": {"bytesInOutput": 2435}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/unused_let_declaration/index.mjs": {"bytesInOutput": 1674}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/checks/skip_hydration_not_static/index.mjs": {"bytesInOutput": 1293}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/src/extended_template_checker.mjs": {"bytesInOutput": 2079}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/adapter.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/interfaces.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/options.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/api/src/public_options.mjs": {"bytesInOutput": 281}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/extended/index.mjs": {"bytesInOutput": 433}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/template_semantics/src/template_semantics_checker.mjs": {"bytesInOutput": 4458}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/initializer_api_usage_rule.mjs": {"bytesInOutput": 2460}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/rules/unused_standalone_imports_rule.mjs": {"bytesInOutput": 3930}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/config.mjs": {"bytesInOutput": 51}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/src/source_file_validator.mjs": {"bytesInOutput": 1386}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/validation/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/core_version.mjs": {"bytesInOutput": 551}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/feature_detection.mjs": {"bytesInOutput": 246}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/core/src/host.mjs": {"bytesInOutput": 7110}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/program.mjs": {"bytesInOutput": 134}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/entry_points.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/perform_compile.mjs": {"bytesInOutput": 7647}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/transformers/util.mjs": {"bytesInOutput": 267}}, "bytes": 203310}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 13821}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-ISOL2WT5.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}], "exports": ["angularJitApplicationTransform", "getDownlevelDecoratorsTransform", "getInitializerApiJitTransform"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/downlevel_decorators_transform.mjs": {"bytesInOutput": 13586}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform.mjs": {"bytesInOutput": 2270}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/transform_api.mjs": {"bytesInOutput": 737}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/input_function.mjs": {"bytesInOutput": 1281}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/model_function.mjs": {"bytesInOutput": 1914}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/output_function.mjs": {"bytesInOutput": 914}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/initializer_api_transforms/query_functions.mjs": {"bytesInOutput": 1510}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/src/index.mjs": {"bytesInOutput": 776}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/jit/index.mjs": {"bytesInOutput": 0}}, "bytes": 25005}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 353443}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-X2KDZPBI.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["CompilationMode", "ComponentDecoratorHandler", "ComponentScopeKind", "CompoundComponentScopeReader", "CompoundMetadataReader", "CompoundMetadataRegistry", "DirectiveDecoratorHandler", "DtsMetadataReader", "DtsTransformRegistry", "DynamicValue", "ExportedProviderStatusResolver", "HostDirectivesResolver", "INPUT_INITIALIZER_FN", "InjectableClassRegistry", "InjectableDecoratorHandler", "JitDeclarationRegistry", "LocalMetadataRegistry", "LocalModuleScopeRegistry", "MODEL_INITIALIZER_FN", "<PERSON><PERSON><PERSON><PERSON>", "MetadataDtsModuleScopeResolver", "NgModuleDecoratorHandler", "NgOriginalFile", "NoopReferencesRegistry", "OUTPUT_INITIALIZER_FNS", "OptimizeFor", "PartialEvaluator", "PipeDecoratorHandler", "PotentialImportKind", "PotentialImportMode", "QUERY_INITIALIZER_FNS", "ResourceRegistry", "SemanticDepGraphUpdater", "ShimAdapter", "ShimReferenceTagger", "StaticInterpreter", "SymbolKind", "TemplateTypeCheckerImpl", "TraitCompiler", "TsCreateProgramDriver", "TypeCheckScopeRegistry", "TypeCheckShimGenerator", "aliasTransformFactory", "createForwardRefResolver", "declarationTransformFactory", "getAngularDecorators", "isAngularDecorator", "<PERSON><PERSON><PERSON>", "ivyTransformFactory", "queryDecoratorNames", "retagAllTsFiles", "tryParseInitializerApi", "tryParseInitializerBasedOutput", "tryParseSignalInputMapping", "tryParseSignalModelMapping", "tryParseSignalQueryFromInitializer", "untagAllTsFiles"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/util.mjs": {"bytesInOutput": 9011}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/dynamic.mjs": {"bytesInOutput": 2296}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interpreter.mjs": {"bytesInOutput": 23248}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/result.mjs": {"bytesInOutput": 669}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/builtin.mjs": {"bytesInOutput": 1261}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/synthetic.mjs": {"bytesInOutput": 92}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/interface.mjs": {"bytesInOutput": 643}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/src/diagnostics.mjs": {"bytesInOutput": 4546}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/partial_evaluator/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/checker.mjs": {"bytesInOutput": 212}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/scope.mjs": {"bytesInOutput": 534}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/api.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/completion.mjs": {"bytesInOutput": 303}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/context.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/api/symbols.mjs": {"bytesInOutput": 766}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/api.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/di.mjs": {"bytesInOutput": 5617}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/diagnostics.mjs": {"bytesInOutput": 11148}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/api.mjs": {"bytesInOutput": 433}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/dts.mjs": {"bytesInOutput": 8053}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/property_mapping.mjs": {"bytesInOutput": 2538}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/util.mjs": {"bytesInOutput": 6689}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/inheritance.mjs": {"bytesInOutput": 2094}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/registry.mjs": {"bytesInOutput": 1620}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/resource_registry.mjs": {"bytesInOutput": 2266}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/providers.mjs": {"bytesInOutput": 1352}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/metadata/src/host_directives_resolver.mjs": {"bytesInOutput": 2282}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/api.mjs": {"bytesInOutput": 588}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/alias.mjs": {"bytesInOutput": 769}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/compilation.mjs": {"bytesInOutput": 17634}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/trait.mjs": {"bytesInOutput": 1880}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/declaration.mjs": {"bytesInOutput": 4855}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/transform/src/transform.mjs": {"bytesInOutput": 10790}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/visitor.mjs": {"bytesInOutput": 2032}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/evaluation.mjs": {"bytesInOutput": 1836}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/factory.mjs": {"bytesInOutput": 591}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/injectable_registry.mjs": {"bytesInOutput": 704}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/metadata.mjs": {"bytesInOutput": 4745}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/debug_info.mjs": {"bytesInOutput": 701}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/references_registry.mjs": {"bytesInOutput": 75}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/schema.mjs": {"bytesInOutput": 1168}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/input_transforms.mjs": {"bytesInOutput": 457}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/common/src/jit_declaration_registry.mjs": {"bytesInOutput": 87}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/handler.mjs": {"bytesInOutput": 55460}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/api.mjs": {"bytesInOutput": 378}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/graph.mjs": {"bytesInOutput": 4383}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/type_parameters.mjs": {"bytesInOutput": 654}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/incremental/semantic_graph/src/util.mjs": {"bytesInOutput": 1072}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/api.mjs": {"bytesInOutput": 260}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/component_scope.mjs": {"bytesInOutput": 545}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/dependency.mjs": {"bytesInOutput": 2497}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/local.mjs": {"bytesInOutput": 16215}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/util.mjs": {"bytesInOutput": 1711}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/scope/src/typecheck.mjs": {"bytesInOutput": 3139}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/handler.mjs": {"bytesInOutput": 9525}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/shared.mjs": {"bytesInOutput": 41048}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_function_access.mjs": {"bytesInOutput": 547}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/initializer_functions.mjs": {"bytesInOutput": 2977}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_output_parse_options.mjs": {"bytesInOutput": 704}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/input_function.mjs": {"bytesInOutput": 1077}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/model_function.mjs": {"bytesInOutput": 1243}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/output_function.mjs": {"bytesInOutput": 1391}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/query_functions.mjs": {"bytesInOutput": 3395}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/src/symbol.mjs": {"bytesInOutput": 3106}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/directive/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/handler.mjs": {"bytesInOutput": 27786}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/src/module_with_providers.mjs": {"bytesInOutput": 2987}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/ng_module/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/diagnostics.mjs": {"bytesInOutput": 908}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/resources.mjs": {"bytesInOutput": 13558}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/symbol.mjs": {"bytesInOutput": 1557}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/src/util.mjs": {"bytesInOutput": 3752}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/checker.mjs": {"bytesInOutput": 26306}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/api.mjs": {"bytesInOutput": 245}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/program_driver/src/ts_create_program_driver.mjs": {"bytesInOutput": 5478}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/adapter.mjs": {"bytesInOutput": 3203}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/expando.mjs": {"bytesInOutput": 1487}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/util.mjs": {"bytesInOutput": 144}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/shims/src/reference_tagger.mjs": {"bytesInOutput": 989}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/diagnostic.mjs": {"bytesInOutput": 3745}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/diagnostics/src/id.mjs": {"bytesInOutput": 397}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/completion.mjs": {"bytesInOutput": 5355}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/comments.mjs": {"bytesInOutput": 4538}, "node_modules/magic-string/dist/magic-string.es.mjs": {"bytesInOutput": 32019}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/context.mjs": {"bytesInOutput": 10137}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/dom.mjs": {"bytesInOutput": 2870}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/environment.mjs": {"bytesInOutput": 2645}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/reference_emit_environment.mjs": {"bytesInOutput": 1688}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/ts_util.mjs": {"bytesInOutput": 2809}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_constructor.mjs": {"bytesInOutput": 4783}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/tcb_util.mjs": {"bytesInOutput": 3510}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_parameter_emitter.mjs": {"bytesInOutput": 2781}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/oob.mjs": {"bytesInOutput": 12979}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/shim.mjs": {"bytesInOutput": 505}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_block.mjs": {"bytesInOutput": 65654}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/diagnostics.mjs": {"bytesInOutput": 1599}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/expression.mjs": {"bytesInOutput": 14293}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/type_check_file.mjs": {"bytesInOutput": 2032}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/source.mjs": {"bytesInOutput": 1550}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/line_mappings.mjs": {"bytesInOutput": 1140}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/src/template_symbol_builder.mjs": {"bytesInOutput": 21716}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/typecheck/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/metadata.mjs": {"bytesInOutput": 925}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/extract_dependencies.mjs": {"bytesInOutput": 8385}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/hmr/src/update_declaration.mjs": {"bytesInOutput": 1457}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/component/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/injectable.mjs": {"bytesInOutput": 9622}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/annotations/src/pipe.mjs": {"bytesInOutput": 6825}}, "bytes": 617247}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/linker/index.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-IYDTONLQ.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs", "inputs": {}, "bytes": 597}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 9150}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/linker/babel/index.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-IYDTONLQ.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-FKXFEX7K.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["createEs2015LinkerPlugin", "default"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/es2015_linker_plugin.mjs": {"bytesInOutput": 3549}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_factory.mjs": {"bytesInOutput": 4705}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/ast/babel_ast_host.mjs": {"bytesInOutput": 4406}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_declaration_scope.mjs": {"bytesInOutput": 796}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/src/babel_plugin.mjs": {"bytesInOutput": 214}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/babel/index.mjs": {"bytesInOutput": 41}}, "bytes": 15150}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-IYDTONLQ.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 26808}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-IYDTONLQ.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js", "kind": "import-statement"}], "exports": ["DEFAULT_LINKER_OPTIONS", "FatalLinkerError", "FileLinker", "LinkerEnvironment", "assert", "isFatalLinkerError", "needsLinking"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/fatal_linker_error.mjs": {"bytesInOutput": 242}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/utils.mjs": {"bytesInOutput": 160}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/ast/ast_value.mjs": {"bytesInOutput": 3982}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/emit_scope.mjs": {"bytesInOutput": 1390}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/linker_import_generator.mjs": {"bytesInOutput": 605}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/emit_scopes/local_emit_scope.mjs": {"bytesInOutput": 375}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_linker_selector.mjs": {"bytesInOutput": 4436}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/get_source_file.mjs": {"bytesInOutput": 305}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_async_linker_1.mjs": {"bytesInOutput": 1125}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_class_metadata_linker_1.mjs": {"bytesInOutput": 629}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_component_linker_1.mjs": {"bytesInOutput": 9694}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_directive_linker_1.mjs": {"bytesInOutput": 6425}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/util.mjs": {"bytesInOutput": 2193}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_factory_linker_1.mjs": {"bytesInOutput": 1054}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injectable_linker_1.mjs": {"bytesInOutput": 1423}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_injector_linker_1.mjs": {"bytesInOutput": 760}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_ng_module_linker_1.mjs": {"bytesInOutput": 2414}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/partial_linkers/partial_pipe_linker_1.mjs": {"bytesInOutput": 943}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/file_linker.mjs": {"bytesInOutput": 2074}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_options.mjs": {"bytesInOutput": 126}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/translator.mjs": {"bytesInOutput": 477}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/linker_environment.mjs": {"bytesInOutput": 1029}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/linker/src/file_linker/needs_linking.mjs": {"bytesInOutput": 105}}, "bytes": 45546}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 73702}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-XRSJELB6.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}], "exports": ["AbsoluteModuleStrategy", "AliasStrategy", "AmbientImport", "COMPILER_ERRORS_WITH_GUIDES", "ClassMemberAccessLevel", "ClassMemberKind", "Context", "DefaultImportTracker", "DeferredSymbolTracker", "ERROR_DETAILS_PAGE_BASE_URL", "ErrorCode", "ExpressionTranslatorVisitor", "ExtendedTemplateDiagnosticName", "FatalDiagnosticError", "ImportFlags", "ImportManager", "ImportedSymbolsTracker", "LocalCompilationExtraImportsTracker", "LocalIdentifierStrategy", "LogicalProjectStrategy", "ModuleResolver", "NoopImportRewriter", "PrivateExportAliasingHost", "R3SymbolsImportRewriter", "Reference", "ReferenceEmitKind", "ReferenceEmitter", "RelativePathStrategy", "TypeEmitter", "TypeEntityToDeclarationError", "TypeScriptReflectionHost", "UnifiedModulesAliasingHost", "UnifiedModulesStrategy", "addDiagnostic<PERSON><PERSON><PERSON>", "assertSuccessfulReferenceEmit", "attachDefaultImportDeclaration", "canEmitType", "classMemberAccessLevelToString", "entityNameToValue", "filterToMembersWithDecorator", "getDefaultImportDeclaration", "getProjectRelativePath", "getRootDirs", "getSourceFile", "getSourceFileOrNull", "getTokenAtPosition", "identifierOfNode", "isAliasImportDeclaration", "isAssignment", "isDeclaration", "isDtsPath", "isFatalDiagnosticError", "isFromDtsFile", "isLocalCompilationDiagnostics", "isNamedClassDeclaration", "isNonDeclarationTsPath", "isSymbolWithValueDeclaration", "loadIsReferencedAliasDeclarationPatch", "makeDiagnostic", "makeDiagno<PERSON><PERSON><PERSON><PERSON>", "makeRelatedInformation", "ngErrorCode", "nodeDebugInfo", "nodeNameForError", "normalizeSeparators", "presetImportManagerForceNamespaceImports", "reflectClassMember", "reflectObjectLiteral", "reflectTypeEntityToDeclaration", "relativePathBetween", "replaceTsWithNgInErrors", "toUnredirectedSourceFile", "translateExpression", "translateStatement", "translateType", "typeNodeToValueExpr"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_code.mjs": {"bytesInOutput": 9318}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/util.mjs": {"bytesInOutput": 235}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error.mjs": {"bytesInOutput": 2073}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/docs.mjs": {"bytesInOutput": 375}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.mjs": {"bytesInOutput": 64}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/diagnostics/src/extended_template_diagnostic_name.mjs": {"bytesInOutput": 1401}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/typescript.mjs": {"bytesInOutput": 18320}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/host.mjs": {"bytesInOutput": 1231}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/type_to_value.mjs": {"bytesInOutput": 5386}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/src/util.mjs": {"bytesInOutput": 673}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/reflection/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/typescript.mjs": {"bytesInOutput": 3362}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/references.mjs": {"bytesInOutput": 1974}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/alias.mjs": {"bytesInOutput": 2001}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/emitter.mjs": {"bytesInOutput": 8741}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/find_export.mjs": {"bytesInOutput": 517}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/util/src/path.mjs": {"bytesInOutput": 599}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/core.mjs": {"bytesInOutput": 2164}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/patch_alias_reference_resolution.mjs": {"bytesInOutput": 1775}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/default.mjs": {"bytesInOutput": 1342}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/deferred_symbol_tracker.mjs": {"bytesInOutput": 4253}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/imported_symbols_tracker.mjs": {"bytesInOutput": 3084}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/local_compilation_extra_imports_tracker.mjs": {"bytesInOutput": 1652}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/imports/src/resolver.mjs": {"bytesInOutput": 642}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_manager.mjs": {"bytesInOutput": 11465}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/check_unique_identifier_name.mjs": {"bytesInOutput": 1072}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/import_typescript_transform.mjs": {"bytesInOutput": 2862}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_generated_imports.mjs": {"bytesInOutput": 1148}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/import_manager/reuse_source_file_imports.mjs": {"bytesInOutput": 2625}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/context.mjs": {"bytesInOutput": 288}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/translator.mjs": {"bytesInOutput": 12082}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_emitter.mjs": {"bytesInOutput": 2741}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/type_translator.mjs": {"bytesInOutput": 9529}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/ts_util.mjs": {"bytesInOutput": 307}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_ast_factory.mjs": {"bytesInOutput": 8924}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/translator/src/typescript_translator.mjs": {"bytesInOutput": 541}}, "bytes": 131303}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/bazel.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["PerfPhase"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/bazel.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/bazel.mjs": {"bytesInOutput": 0}}, "bytes": 475}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 4684}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-Q2WE7ECN.js": {"imports": [], "exports": ["ActivePerfRecorder", "DelegatingPerfRecorder", "PerfCheckpoint", "PerfEvent", "PerfPhase"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/api.mjs": {"bytesInOutput": 4308}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/index.mjs": {"bytesInOutput": 0}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/noop.mjs": {"bytesInOutput": 231}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/clock.mjs": {"bytesInOutput": 178}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/perf/src/recorder.mjs": {"bytesInOutput": 2524}}, "bytes": 8050}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/private/localize.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-FKXFEX7K.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js", "kind": "import-statement"}, {"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel", "LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "SourceFile", "SourceFileLoader", "absoluteFrom", "absoluteFromSourceFile", "basename", "createFileSystemTsReadDirectoryFn", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "toRelativeImport"], "entryPoint": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/localize.mjs", "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/private/localize.mjs": {"bytesInOutput": 0}}, "bytes": 1368}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-FKXFEX7K.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 940}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-FKXFEX7K.js": {"imports": [], "exports": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/logger.mjs": {"bytesInOutput": 254}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/src/console_logger.mjs": {"bytesInOutput": 675}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/logging/index.mjs": {"bytesInOutput": 0}}, "bytes": 1527}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8833}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-77D5CI2U.js": {"imports": [], "exports": ["SourceFile", "SourceFileLoader"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file.mjs": {"bytesInOutput": 8842}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/segment_marker.mjs": {"bytesInOutput": 548}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/source_file_loader.mjs": {"bytesInOutput": 4539}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/src/content_origin.mjs": {"bytesInOutput": 279}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/sourcemaps/index.mjs": {"bytesInOutput": 0}}, "bytes": 15215}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8428}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-37JMVF7H.js": {"imports": [{"path": "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js", "kind": "import-statement"}], "exports": ["LogicalFileSystem", "LogicalProjectPath", "NgtscCompilerHost", "NodeJSFileSystem", "absoluteFrom", "absoluteFromSourceFile", "basename", "createFileSystemTsReadDirectoryFn", "dirname", "getFileSystem", "getSourceFileOrError", "isLocalRelativePath", "isRoot", "isRooted", "join", "relative", "relativeFrom", "resolve", "setFileSystem", "stripExtension", "toRelativeImport"], "inputs": {"bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/util.mjs": {"bytesInOutput": 495}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/invalid_file_system.mjs": {"bytesInOutput": 1486}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/helpers.mjs": {"bytesInOutput": 1506}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/compiler_host.mjs": {"bytesInOutput": 1663}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/logical.mjs": {"bytesInOutput": 1709}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/node_js_file_system.mjs": {"bytesInOutput": 2656}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/src/ts_read_directory.mjs": {"bytesInOutput": 1366}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/src/ngtsc/file_system/index.mjs": {"bytesInOutput": 0}}, "bytes": 12369}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 69}, "bazel-out/k8-fastbuild/bin/packages/compiler-cli/bundles/chunk-KPQ72R34.js": {"imports": [], "exports": ["__publicField", "__require"], "inputs": {}, "bytes": 924}}}