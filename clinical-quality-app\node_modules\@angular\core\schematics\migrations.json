{"schematics": {"explicit-standalone-flag": {"version": "19.0.0", "description": "Updates non-standalone Directives, Component and Pipes to 'standalone:false' and removes 'standalone:true' from those who are standalone", "factory": "./bundles/explicit-standalone-flag#migrate"}, "pending-tasks": {"version": "19.0.0", "description": "Updates ExperimentalPendingTasks to PendingTasks", "factory": "./bundles/pending-tasks#migrate"}, "provide-initializer": {"version": "19.0.0", "description": "Replaces `APP_INITIALIZER`, `ENVIRONMENT_INITIALIZER` & `PLATFORM_INITIALIZER` respectively with `provideAppInitializer`, `provideEnvironmentInitializer` & `providePlatformInitializer`.", "factory": "./bundles/provide-initializer#migrate", "optional": true}}}