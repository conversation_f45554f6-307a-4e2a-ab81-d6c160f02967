{"$schema": "http://json-schema.org/draft-07/schema", "$id": "angular-material-ng-add", "title": "Angular Material ng-add schematic", "type": "object", "properties": {"project": {"type": "string", "description": "Name of the project.", "$default": {"$source": "projectName"}}, "theme": {"description": "The theme to apply", "type": "string", "default": "azure-blue", "x-prompt": {"message": "Choose a prebuilt theme name, or \"custom\" for a custom theme:", "type": "list", "items": [{"value": "azure-blue", "label": "Azure/Blue         [Preview: https://material.angular.io?theme=azure-blue]"}, {"value": "rose-red", "label": "Rose/Red           [Preview: https://material.angular.io?theme=rose-red]"}, {"value": "magenta-violet", "label": "Magenta/Violet     [Preview: https://material.angular.io?theme=magenta-violet]"}, {"value": "cyan-orange", "label": "Cyan/Orange        [Preview: https://material.angular.io?theme=cyan-orange]"}, {"value": "custom", "label": "Custom"}]}}, "typography": {"type": "boolean", "default": false, "description": "Whether to set up global typography styles.", "x-prompt": "Set up global Angular Material typography styles?"}}, "required": []}