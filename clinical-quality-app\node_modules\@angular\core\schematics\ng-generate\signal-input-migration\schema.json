{"$schema": "http://json-schema.org/draft-07/schema", "$id": "AngularSignalInputMigration", "title": "Angular Signal Input migration", "type": "object", "properties": {"path": {"type": "string", "description": "Path to the directory where all inputs should be migrated.", "x-prompt": "Which directory do you want to migrate?", "default": "./"}, "analysisDir": {"type": "string", "description": "Path to the directory that should be analyzed. References to migrated inputs are migrated based on this folder. Useful for larger projects if the analysis takes too long and the analysis scope can be narrowed.", "default": "./"}, "bestEffortMode": {"type": "boolean", "description": "Whether to eagerly migrate as much as possible, ignoring problematic patterns that would otherwise prevent migration.", "x-prompt": "Do you want to migrate as much as possible, even if it may break your build?", "default": false}, "insertTodos": {"type": "boolean", "description": "Whether the migration should add TODOs for inputs that could not be migrated", "default": false}}}